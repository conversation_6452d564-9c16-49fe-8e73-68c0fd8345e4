.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;

  h2 {
    margin: 0;
    font-size: 1.75rem;
  }

  .actions {
    display: flex;
    gap: 10px;
  }
}

.status-bar {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .alert {
    margin: 0;
    padding: 0.75rem 1.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 1.1rem;
    }
  }

  .alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
  }

  .alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }
}