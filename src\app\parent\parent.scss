.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  background-color: var(--cui-body-bg);
  color: var(--cui-body-color);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--cui-border-color);

  h2 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--cui-emphasis-color);
  }

  .actions {
    display: flex;
    gap: 0.75rem;

    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: var(--cui-border-radius);
      transition: all 0.15s ease-in-out;

      c-icon {
        font-size: 1rem;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
    }
  }
}

.status-bar {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .alert {
    margin: 0;
    padding: 0.75rem 1rem;
    border-radius: var(--cui-border-radius);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid;
    font-size: 0.875rem;

    c-icon {
      font-size: 1rem;
    }
  }

  .alert-info {
    color: var(--cui-info-text-emphasis);
    background-color: var(--cui-info-bg-subtle);
    border-color: var(--cui-info-border-subtle);
  }

  .alert-success {
    color: var(--cui-success-text-emphasis);
    background-color: var(--cui-success-bg-subtle);
    border-color: var(--cui-success-border-subtle);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }
}