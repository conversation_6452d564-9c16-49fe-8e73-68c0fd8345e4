<div class="table-container" [class.loading]="isLoading">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="loading-text">Loading data...</div>
  </div>

  <!-- Filter Controls -->
  <div *ngIf="config.showFilter" class="table-controls mb-3">
    <div class="input-group">
      <span class="input-group-text">
        <c-icon name="cilMagnifyingGlass"></c-icon>
      </span>
      <input #filterInput
             type="text"
             class="form-control"
             placeholder="Filter records..."
             [(ngModel)]="filterText"
             (input)="applyFilter()">
      <button *ngIf="filterText"
              class="btn btn-outline-secondary"
              type="button"
              (click)="clearFilter()">
        <c-icon name="cilX"></c-icon> Clear
      </button>
    </div>
    <div *ngIf="filteredData.length !== _data.length" class="filtered-count">
      Showing {{filteredData.length}} of {{_data.length}} records
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && _data.length === 0" class="empty-state">
    <c-icon name="cilInfo"></c-icon>
    <div>{{config.emptyMessage}}</div>
  </div>

  <!-- Table Content -->
  <div *ngIf="!isLoading && _data.length > 0" class="table-responsive">
    <table class="table"
           [class.table-striped]="config.striped"
           [class.table-hover]="config.hover"
           [class.table-bordered]="config.bordered"
           [class.table-sm]="config.small">
      <thead>
        <tr>
          <!-- Selection Column -->
          <th *ngIf="config.selectable" scope="col" class="select-column">
            <input *ngIf="config.multiSelect" 
                   type="checkbox" 
                   class="form-check-input"
                   [checked]="isAllSelected()"
                   [indeterminate]="selectedRows.size > 0 && !isAllSelected()"
                   (change)="masterToggle()">
          </th>

          <!-- Data Columns -->
          <th *ngFor="let column of columns"
              scope="col"
              [class]="getColumnClass(column)"
              [class.sortable]="column.sortable"
              (click)="column.sortable ? onSort(column.name) : null">
            {{column.displayName}}
            <c-icon *ngIf="column.sortable" [name]="getSortIcon(column.name)" class="ms-2"></c-icon>
          </th>

          <!-- Actions Column -->
          <th *ngIf="showActionsColumn && visibleActions.length > 0" 
              scope="col"
              class="actions-header">
            Actions
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of paginatedData" 
            [class.selected]="isSelected(row)">
          <!-- Selection Checkbox -->
          <td *ngIf="config.selectable" class="select-column">
            <input type="checkbox" 
                   class="form-check-input"
                   [checked]="isSelected(row)"
                   (change)="toggleRowSelection(row)">
          </td>

          <!-- Data Cells -->
          <td *ngFor="let column of columns"
              [class]="getColumnClass(column)">
            <ng-container [ngSwitch]="column.dataType">
              <span *ngSwitchCase="'date'">{{ row[column.name] | date }}</span>
              <span *ngSwitchCase="'boolean'" class="boolean-cell">
                <span [class]="row[column.name] ? 'badge bg-success' : 'badge bg-danger'">
                  {{ row[column.name] ? '✓ Active' : '✗ Inactive' }}
                </span>
              </span>
              <span *ngSwitchDefault>{{ row[column.name] }}</span>
            </ng-container>
          </td>

          <!-- Action Buttons -->
          <td *ngIf="showActionsColumn && visibleActions.length > 0"
              class="action-buttons">
            <div class="btn-group btn-group-sm" role="group">
              <ng-container *ngFor="let action of visibleActions">
                <!-- Icon-only for common actions -->
                <button *ngIf="action.icon && !action.label && shouldShowAction(action, row)"
                        class="btn icon-btn"
                        [class]="'btn-outline-' + (action.color || 'primary')"
                        [title]="action.name | titlecase"
                        (click)="onActionClick(action.name, row)">
                  <c-icon [name]="getIconName(action.icon)"></c-icon>
                </button>

                <!-- Text + icon for less common actions -->
                <button *ngIf="action.label && shouldShowAction(action, row)"
                        class="btn text-btn"
                        [class]="'btn-outline-' + (action.color || 'primary')"
                        (click)="onActionClick(action.name, row)">
                  <c-icon *ngIf="action.icon" [name]="getIconName(action.icon)" class="me-1"></c-icon>
                  {{action.label}}
                </button>
              </ng-container>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Pagination -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span class="text-muted">
          Showing {{(currentPage - 1) * pageSize + 1}} to {{getEndIndex()}}
          of {{filteredData.length}} entries
        </span>
      </div>

      <div class="pagination-controls">
        <div class="form-group mb-0 me-3">
          <select class="form-select form-select-sm"
                  [(ngModel)]="pageSize"
                  (change)="onPageSizeChange(pageSize)">
            <option *ngFor="let size of config.pageSizeOptions" [value]="size">{{size}} per page</option>
          </select>
        </div>

        <nav>
          <ul class="pagination pagination-sm mb-0">
            <li class="page-item" [class.disabled]="currentPage === 1">
              <a class="page-link" (click)="currentPage > 1 && onPageChange(1)" aria-label="First">
                <c-icon name="cilMediaStepBackward"></c-icon>
              </a>
            </li>
            <li class="page-item" [class.disabled]="currentPage === 1">
              <a class="page-link" (click)="currentPage > 1 && onPageChange(currentPage - 1)" aria-label="Previous">
                <c-icon name="cilChevronLeft"></c-icon>
              </a>
            </li>

            <li *ngFor="let page of getPages()"
                class="page-item"
                [class.disabled]="page === -1"
                [class.active]="page === currentPage && page !== -1">
              <a *ngIf="page !== -1" class="page-link" (click)="onPageChange(page)">{{page}}</a>
              <span *ngIf="page === -1" class="page-link">...</span>
            </li>

            <li class="page-item" [class.disabled]="currentPage === totalPages || totalPages === 0">
              <a class="page-link" (click)="currentPage < totalPages && onPageChange(currentPage + 1)" aria-label="Next">
                <c-icon name="cilChevronRight"></c-icon>
              </a>
            </li>
            <li class="page-item" [class.disabled]="currentPage === totalPages || totalPages === 0">
              <a class="page-link" (click)="currentPage < totalPages && onPageChange(totalPages)" aria-label="Last">
                <c-icon name="cilMediaStepForward"></c-icon>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</div>