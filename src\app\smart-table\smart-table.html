<div class="table-container" [class.loading]="isLoading">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="loading-text">Loading data...</div>
  </div>

  <!-- Filter Controls -->
  <div *ngIf="config.showFilter" class="table-controls mb-3">
    <div class="input-group">
      <span class="input-group-text">
        <i class="cil-magnifying-glass"></i>
      </span>
      <input #filterInput 
             type="text" 
             class="form-control" 
             placeholder="Filter records..." 
             [(ngModel)]="filterText"
             (input)="applyFilter()">
      <button *ngIf="filterText" 
              class="btn btn-outline-secondary" 
              type="button"
              (click)="clearFilter()">
        <i class="cil-x"></i> Clear
      </button>
    </div>
    <div *ngIf="filteredData.length !== _data.length" class="filtered-count">
      Showing {{filteredData.length}} of {{_data.length}} records
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && _data.length === 0" class="empty-state">
    <i class="cil-info"></i>
    <div>{{config.emptyMessage}}</div>
  </div>

  <!-- Table Content -->
  <div *ngIf="!isLoading && _data.length > 0" class="table-responsive">
    <table class="table"
           [class.table-striped]="config.striped"
           [class.table-hover]="config.hover"
           [class.table-bordered]="config.bordered"
           [class.table-sm]="config.small">
      <thead>
        <tr>
          <!-- Selection Column -->
          <th *ngIf="config.selectable" scope="col" class="select-column">
            <input *ngIf="config.multiSelect" 
                   type="checkbox" 
                   class="form-check-input"
                   [checked]="isAllSelected()"
                   [indeterminate]="selectedRows.size > 0 && !isAllSelected()"
                   (change)="masterToggle()">
          </th>

          <!-- Data Columns -->
          <th *ngFor="let column of columns" 
              scope="col"
              [class]="getColumnClass(column)"
              [class.sortable]="column.sortable"
              (click)="column.sortable ? onSort(column.name) : null">
            {{column.displayName}}
            <i *ngIf="column.sortable" class="ms-2 {{getSortIcon(column.name)}}"></i>
          </th>

          <!-- Actions Column -->
          <th *ngIf="showActionsColumn && visibleActions.length > 0" 
              scope="col"
              class="actions-header">
            Actions
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of paginatedData" 
            [class.selected]="isSelected(row)">
          <!-- Selection Checkbox -->
          <td *ngIf="config.selectable" class="select-column">
            <input type="checkbox" 
                   class="form-check-input"
                   [checked]="isSelected(row)"
                   (change)="toggleRowSelection(row)">
          </td>

          <!-- Data Cells -->
          <td *ngFor="let column of columns"
              [class]="getColumnClass(column)">
            <ng-container [ngSwitch]="column.dataType">
              <span *ngSwitchCase="'date'">{{ row[column.name] | date }}</span>
              <span *ngSwitchCase="'boolean'">
                <i [class]="row[column.name] ? 'cil-check-circle text-success' : 'cil-x-circle text-danger'"></i>
              </span>
              <span *ngSwitchDefault>{{ row[column.name] }}</span>
            </ng-container>
          </td>

          <!-- Action Buttons -->
          <td *ngIf="showActionsColumn && visibleActions.length > 0" 
              class="action-buttons">
            <ng-container *ngFor="let action of visibleActions">
              <!-- Icon-only for common actions -->
              <button *ngIf="action.icon && !action.label" 
                      class="btn btn-sm icon-btn"
                      [class]="'btn-' + (action.color || 'primary')"
                      [title]="action.name | titlecase"
                      (click)="onActionClick(action.name, row)"
                      [disabled]="!shouldShowAction(action, row)">
                <i [class]="action.icon"></i>
              </button>
              
              <!-- Text + icon for less common actions -->
              <button *ngIf="action.label"
                      class="btn btn-sm text-btn"
                      [class]="'btn-' + (action.color || 'primary')"
                      (click)="onActionClick(action.name, row)"
                      [disabled]="!shouldShowAction(action, row)">
                <i *ngIf="action.icon" [class]="action.icon"></i>
                {{action.label}}
              </button>
            </ng-container>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Pagination -->
    <div class="pagination-container">
      <div class="form-group mb-0">
        <select class="form-select form-select-sm" 
                [(ngModel)]="pageSize" 
                (change)="onPageSizeChange(pageSize)">
          <option *ngFor="let size of config.pageSizeOptions" [value]="size">{{size}} items</option>
        </select>
      </div>

      <nav>
        <ul class="pagination pagination-sm mb-0">
          <li class="page-item" [class.disabled]="currentPage === 1">
            <a class="page-link" (click)="onPageChange(1)" aria-label="First">
              <span aria-hidden="true">&laquo;&laquo;</span>
            </a>
          </li>
          <li class="page-item" [class.disabled]="currentPage === 1">
            <a class="page-link" (click)="onPageChange(currentPage - 1)" aria-label="Previous">
              <span aria-hidden="true">&laquo;</span>
            </a>
          </li>
          
          <li *ngFor="let page of getPages()" 
              class="page-item" 
              [class.disabled]="page === -1"
              [class.active]="page === currentPage && page !== -1">
            <a *ngIf="page !== -1" class="page-link" (click)="onPageChange(page)">{{page}}</a>
            <span *ngIf="page === -1" class="page-link">...</span>
          </li>
          
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <a class="page-link" (click)="onPageChange(currentPage + 1)" aria-label="Next">
              <span aria-hidden="true">&raquo;</span>
            </a>
          </li>
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <a class="page-link" (click)="onPageChange(totalPages)" aria-label="Last">
              <span aria-hidden="true">&raquo;&raquo;</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</div>