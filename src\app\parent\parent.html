<div class="container">
  <div class="header">
    <h2>User Management</h2>
    <div class="actions">
      <button class="btn btn-primary" (click)="addUser()">
        <c-icon name="cilPlus"></c-icon> Add User
      </button>
      <button class="btn btn-outline-secondary" (click)="refreshData()">
        <c-icon name="cilReload"></c-icon> Refresh
      </button>
    </div>
  </div>

  <div class="status-bar">
    @if (lastAction) {
      <div class="alert alert-info">
        Last action: <strong>{{lastAction}}</strong>
      </div>
    }
    @if (selectedRows.length) {
      <div class="alert alert-success">
        Selected {{selectedRows.length}} row(s):
        IDs: {{getSelectedIds()}}
      </div>
    }
  </div>

  <app-smart-table 
    [columns]="tableColumns"
    [data]="tableData"
    [actions]="tableActions"
    [config]="tableConfig"
    [isLoading]="loading"
    (actionClick)="handleTableAction($event)"
    (selectionChange)="handleSelectionChange($event)">
  </app-smart-table>
</div>