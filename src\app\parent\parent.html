<div class="container">
  <div class="header">
    <h2>User Management</h2>
    <div class="actions">
      <button class="btn btn-primary" (click)="addUser()">
        <i class="cil-plus"></i> Add User
      </button>
      <button class="btn btn-outline-secondary" (click)="refreshData()">
        <i class="cil-reload"></i> Refresh
      </button>
    </div>
  </div>

  <div class="status-bar">
    <div *ngIf="lastAction" class="alert alert-info">
      Last action: <strong>{{lastAction}}</strong>
    </div>
    <div *ngIf="selectedRows.length" class="alert alert-success">
      Selected {{selectedRows.length}} row(s): 
      IDs: {{selectedRows.map(row => row.id).join(', ')}}
    </div>
  </div>

  <app-smart-table 
    [columns]="tableColumns"
    [data]="tableData"
    [actions]="tableActions"
    [config]="tableConfig"
    [isLoading]="loading"
    (actionClick)="handleTableAction($event)"
    (selectionChange)="handleSelectionChange($event)">
  </app-smart-table>
</div>