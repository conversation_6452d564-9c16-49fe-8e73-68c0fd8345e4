import { Component } from '@angular/core';
import { TableColumn, TableAction, TableConfig } from '../models/table.models';
import { SmartTableComponent } from '../smart-table/smart-table';

@Component({
  selector: 'app-parent',
  imports: [SmartTableComponent],
  templateUrl: './parent.html',
  styleUrls: ['./parent.scss']
})
export class ParentComponent {
  // Table configuration
  tableColumns: TableColumn[] = [
    { name: 'id', displayName: 'ID', sortable: true, dataType: 'number' },
    { name: 'name', displayName: 'Name', sortable: true },
    { name: 'email', displayName: 'Email', sortable: false },
    { name: 'createdAt', displayName: 'Join Date', sortable: true, dataType: 'date' },
    { name: 'active', displayName: 'Active', sortable: true, dataType: 'boolean' }
  ];

  tableData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', createdAt: '2023-01-15', active: true },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', createdAt: '2023-02-20', active: false },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', createdAt: '2023-03-10', active: true },
    { id: 4, name: 'Alice Brown', email: '<EMAIL>', createdAt: '2023-04-05', active: true },
    { id: 5, name: '<PERSON> <PERSON>', email: '<EMAIL>', createdAt: '2023-05-12', active: false }
  ];

  tableActions: TableAction[] = [
    { 
      name: 'edit', 
      icon: 'cil-pencil', 
      color: 'primary',
      hidden: false
    },
    { 
      name: 'delete', 
      icon: 'cil-trash', 
      color: 'danger',
      condition: (row: any) => row.active,
      hidden: false
    },
    { 
      name: 'view', 
      label: 'View', 
      icon: 'cil-info',
      color: 'info',
      hidden: false
    }
  ];

  tableConfig: TableConfig = {
    pageSizeOptions: [5, 10, 25],
    pageSize: 5,
    selectable: true,
    multiSelect: true,
    emptyMessage: 'No users found',
    striped: true,
    hover: true,
    bordered: false,
    small: false,
    showFilter: true
  };

  loading = false;
  selectedRows: any[] = [];
  lastAction: string = '';

  // Handle table actions
  handleTableAction(event: {action: string, row: any}) {
    this.lastAction = `${event.action} (ID: ${event.row.id})`;
    console.log('Table Action:', event);
    
    switch(event.action) {
      case 'edit':
        this.editUser(event.row);
        break;
      case 'delete':
        this.deleteUser(event.row);
        break;
      case 'view':
        this.viewUser(event.row);
        break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  // Handle row selection changes
  handleSelectionChange(selectedRows: any[]) {
    console.log('Selection Changed:', selectedRows);
    this.selectedRows = selectedRows;
  }

  // Action implementations
  editUser(user: any) {
    console.log('Editing user:', user);
    // In a real app, you would open a modal or navigate to edit page
    alert(`Editing user: ${user.name}\nID: ${user.id}`);
  }

  deleteUser(user: any) {
    console.log('Deleting user:', user);
    if (confirm(`Are you sure you want to delete ${user.name}?`)) {
      this.tableData = this.tableData.filter(u => u.id !== user.id);
      console.log('User deleted:', user);
    }
  }

  viewUser(user: any) {
    console.log('Viewing user:', user);
    alert(`User Details:\n\nID: ${user.id}\nName: ${user.name}\nEmail: ${user.email}\nStatus: ${user.active ? 'Active' : 'Inactive'}\nJoined: ${new Date(user.createdAt).toLocaleDateString()}`);
  }

  // Add a new user (demo method)
  addUser() {
    const newId = Math.max(...this.tableData.map(u => u.id)) + 1;
    const newUser = {
      id: newId,
      name: `New User ${newId}`,
      email: `user${newId}@example.com`,
      createdAt: new Date().toISOString(),
      active: true
    };
    this.tableData = [...this.tableData, newUser];
    console.log('User added:', newUser);
  }

  // Refresh data (demo method)
  refreshData() {
    this.loading = true;
    console.log('Refreshing data...');
    // Simulate API call
    setTimeout(() => {
      this.loading = false;
      console.log('Data refreshed');
    }, 1000);
  }
}