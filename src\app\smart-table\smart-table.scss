:host {
  display: block;
  margin-bottom: 1.5rem;
  position: relative;
}

.table-container {
  position: relative;
  min-height: 200px;
  margin: 1rem 0;
  overflow-x: auto;
  background-color: var(--cui-body-bg);
  border-radius: var(--cui-border-radius);
  box-shadow: var(--cui-box-shadow-sm);

  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(var(--cui-body-bg-rgb), 0.8);
    backdrop-filter: blur(2px);
    z-index: 10;
    margin: 0;
    padding: 1rem;

    .loading-text {
      margin-top: 1rem;
      color: var(--cui-primary);
      font-weight: 500;
    }
  }

  .table-controls {
    padding: 0 1rem;
    
    .input-group {
      max-width: 400px;
    }
    
    .filtered-count {
      font-size: 0.875rem;
      color: var(--cui-secondary-color);
      margin-top: 0.5rem;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    margin: 1rem;
    gap: 0.75rem;
    border-radius: var(--cui-border-radius);
    background-color: var(--cui-info-bg-subtle);
    border: 1px solid var(--cui-info-border-subtle);
    color: var(--cui-info-text-emphasis);

    i {
      font-size: 1.25rem;
      margin-right: 0.5rem;
    }
  }
}

.table-responsive {
  width: 100%;
  min-width: fit-content;
}

.table {
  width: 100%;
  table-layout: auto;
  margin: 0;
  background-color: var(--cui-body-bg);
  border-collapse: separate;
  border-spacing: 0;

  th, td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--cui-border-color);

    &.active-column {
      text-align: center;
      width: 100px;
      
      i {
        font-size: 1rem;
        &.cil-check-circle {
          color: var(--cui-success);
        }
        &.cil-x-circle {
          color: var(--cui-danger);
        }
      }
    }

    &.number-column, &.numeric-column {
      text-align: right;
      font-feature-settings: "tnum";
      font-variant-numeric: tabular-nums;
    }

    &.date-column {
      white-space: nowrap;
    }

    &.boolean-column {
      text-align: center;

      .boolean-cell {
        display: flex;
        justify-content: center;
        align-items: center;

        .badge {
          display: inline-flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.375rem 0.75rem;
          font-size: 0.75rem;
          font-weight: 500;
          border-radius: var(--cui-border-radius-pill);
          border: none;

          c-icon {
            font-size: 0.875rem;
          }
        }
      }
    }

    &.status-column {
      text-align: center;

      .status-cell {
        display: flex;
        justify-content: center;
        align-items: center;

        .badge {
          display: inline-flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.375rem 0.75rem;
          font-size: 0.75rem;
          font-weight: 500;
          border-radius: var(--cui-border-radius-pill);
          border: none;
          white-space: nowrap;
          transition: all 0.15s ease-in-out;

          c-icon {
            font-size: 0.875rem;
          }

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
        }
      }
    }
  }

  th {
    background-color: var(--cui-tertiary-bg);
    border-bottom: 2px solid var(--cui-border-color);
    font-weight: 600;
    color: var(--cui-emphasis-color);
    position: sticky;
    top: 0;
    z-index: 2;

    &.sortable {
      cursor: pointer;
      user-select: none;
      transition: background-color 0.15s ease-in-out;

      &:hover {
        background-color: var(--cui-secondary-bg);
      }

      i {
        transition: transform 0.15s ease-in-out;
        margin-left: 0.5rem;
      }
    }
  }

  tr {
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: var(--cui-tertiary-bg-subtle);
    }

    &.selected {
      background-color: var(--cui-primary-bg-subtle);
      border-color: var(--cui-primary-border-subtle);
    }
  }

  .select-column {
    width: 40px;
    text-align: center;
  }

  .actions-header {
    text-align: center;
    width: 120px;
    min-width: 120px;
    position: sticky;
    right: 0;
    background-color: var(--cui-tertiary-bg);
    z-index: 3;
    box-shadow: -2px 0 4px rgba(0,0,0,0.08);
  }

  .action-buttons {
    text-align: center;
    width: 120px;
    min-width: 120px;
    position: sticky;
    right: 0;
    background-color: inherit;
    z-index: 1;
    box-shadow: -2px 0 4px rgba(0,0,0,0.08);
    padding: 0.5rem 0.25rem;

    .btn-group {
      display: inline-flex;
      gap: 0.25rem;
      justify-content: center;
      width: 100%;

      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
        line-height: 1.2;
        border-radius: var(--cui-border-radius-sm);
        white-space: nowrap;
        transition: all 0.15s ease-in-out;
        border-width: 1px;
        min-height: 28px;

        &.icon-btn {
          padding: 0.25rem;
          min-width: 28px;
          max-width: 28px;

          c-icon {
            font-size: 0.875rem;
          }
        }

        &.text-btn {
          min-width: 60px;
          padding: 0.25rem 0.5rem;

          c-icon {
            font-size: 0.75rem;
          }
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 0.75rem 1rem;
  background-color: var(--cui-tertiary-bg);
  border-top: 1px solid var(--cui-border-color);
  border-radius: 0 0 var(--cui-border-radius) var(--cui-border-radius);

  .pagination-info {
    font-size: 0.875rem;
    color: var(--cui-secondary-color);
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;

    .form-select {
      border-color: var(--cui-border-color);
      padding: 0.25rem 1.75rem 0.25rem 0.5rem;
      width: auto;
      min-width: 120px;
      font-size: 0.875rem;

      &:focus {
        border-color: var(--cui-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
      }
    }
  }
}

.pagination {
  margin: 0;
  padding: 0;

  .page-item {
    margin: 0 0.125rem;

    .page-link {
      padding: 0.375rem 0.5rem;
      margin: 0;
      transition: all 0.15s ease-in-out;
      min-width: 2rem;
      text-align: center;
      font-size: 0.875rem;
      border-color: var(--cui-border-color);
      color: var(--cui-body-color);

      &:hover {
        background-color: var(--cui-tertiary-bg);
        border-color: var(--cui-primary);
        color: var(--cui-primary);
      }

      c-icon {
        font-size: 0.75rem;
      }
    }

    &.active .page-link {
      background-color: var(--cui-primary);
      border-color: var(--cui-primary);
      color: white;
    }

    &.disabled .page-link {
      opacity: 0.5;
      pointer-events: none;
    }
  }
}

.form-check-input {
  margin: 0 0.5rem 0 0;
  width: 1.1em;
  height: 1.1em;

  &:checked {
    background-color: var(--cui-primary);
    border-color: var(--cui-primary);
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
  }
}