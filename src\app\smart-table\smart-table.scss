:host {
  display: block;
  margin-bottom: 1.5rem;
  position: relative;
}

.table-container {
  position: relative;
  min-height: 200px;
  margin: 1rem 0;
  overflow-x: auto;
  background-color: var(--cui-body-bg);
  border-radius: var(--cui-border-radius);
  box-shadow: var(--cui-box-shadow-sm);

  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(var(--cui-body-bg-rgb), 0.8);
    backdrop-filter: blur(2px);
    z-index: 10;
    margin: 0;
    padding: 1rem;

    .loading-text {
      margin-top: 1rem;
      color: var(--cui-primary);
      font-weight: 500;
    }
  }

  .table-controls {
    padding: 0 1rem;
    
    .input-group {
      max-width: 400px;
    }
    
    .filtered-count {
      font-size: 0.875rem;
      color: var(--cui-secondary-color);
      margin-top: 0.5rem;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    margin: 1rem;
    gap: 0.75rem;
    border-radius: var(--cui-border-radius);
    background-color: var(--cui-info-bg-subtle);
    border: 1px solid var(--cui-info-border-subtle);
    color: var(--cui-info-text-emphasis);

    i {
      font-size: 1.25rem;
      margin-right: 0.5rem;
    }
  }
}

.table-responsive {
  width: 100%;
  min-width: fit-content;
}

.table {
  width: 100%;
  table-layout: auto;
  margin: 0;
  background-color: var(--cui-body-bg);
  border-collapse: separate;
  border-spacing: 0;

  th, td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--cui-border-color);

    &.active-column {
      text-align: center;
      width: 100px;
      
      i {
        font-size: 1rem;
        &.cil-check-circle {
          color: var(--cui-success);
        }
        &.cil-x-circle {
          color: var(--cui-danger);
        }
      }
    }

    &.number-column, &.numeric-column {
      text-align: right;
      font-feature-settings: "tnum";
      font-variant-numeric: tabular-nums;
    }

    &.date-column {
      white-space: nowrap;
    }

    &.boolean-column {
      text-align: center;
    }
  }

  th {
    background-color: var(--cui-tertiary-bg);
    border-bottom: 2px solid var(--cui-border-color);
    font-weight: 600;
    color: var(--cui-emphasis-color);
    position: sticky;
    top: 0;
    z-index: 2;

    &.sortable {
      cursor: pointer;
      user-select: none;
      transition: background-color 0.15s ease-in-out;

      &:hover {
        background-color: var(--cui-secondary-bg);
      }

      i {
        transition: transform 0.15s ease-in-out;
        margin-left: 0.5rem;
      }
    }
  }

  tr {
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: var(--cui-tertiary-bg-subtle);
    }

    &.selected {
      background-color: var(--cui-primary-bg-subtle);
      border-color: var(--cui-primary-border-subtle);
    }
  }

  .select-column {
    width: 40px;
    text-align: center;
  }

  .actions-header, .action-buttons {
    position: sticky;
    right: 0;
    background-color: inherit;
    z-index: 1;
    box-shadow: -2px 0 3px rgba(0,0,0,0.05);
  }

  .actions-header {
    z-index: 3;
    background-color: var(--cui-tertiary-bg);
  }

  .action-buttons {
    display: inline-flex;
    gap: 0.375rem;
    justify-content: flex-end;
    min-width: fit-content;
    padding-right: 0.5rem;
    transition: all 0.2s ease;

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.25rem 0.5rem;
      font-size: 0.8125rem;
      line-height: 1.2;
      border-radius: var(--cui-border-radius-sm);
      white-space: nowrap;
      transition: all 0.2s ease;
      opacity: 0.8;
      transform: scale(0.95);

      &.icon-btn {
        padding: 0.25rem;
        min-width: 28px;
        
        i {
          margin: 0;
        }
      }
      
      &.text-btn {
        min-width: 80px;
        gap: 0.25rem;
        
        i {
          font-size: 0.75rem;
        }
      }
    }

    tr:hover & {
      .btn {
        opacity: 1;
        transform: scale(1);
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  background-color: var(--cui-body-bg);
  border-top: 1px solid var(--cui-border-color);
  position: sticky;
  bottom: 0;
  z-index: 1;

  .form-select {
    border-color: var(--cui-border-color);
    margin-right: 1rem;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    width: auto;

    &:focus {
      border-color: var(--cui-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
    }
  }
}

.pagination {
  margin: 0;
  padding: 0;

  .page-item {
    margin: 0 0.25rem;

    .page-link {
      padding: 0.5rem 0.75rem;
      margin: 0;
      transition: all 0.15s ease-in-out;
      min-width: 2.5rem;
      text-align: center;
    }
  }
}

.form-check-input {
  margin: 0 0.5rem 0 0;
  width: 1.1em;
  height: 1.1em;

  &:checked {
    background-color: var(--cui-primary);
    border-color: var(--cui-primary);
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
  }
}